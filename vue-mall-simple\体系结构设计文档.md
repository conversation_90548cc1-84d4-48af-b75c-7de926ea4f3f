# Vue3在线商城系统 - 体系结构设计文档

## 文档介绍

### 0.1 文档目的
本文档是Vue3在线商城系统的体系结构设计报告，主要用于说明系统的设计约束、设计策略、总体结构和架构设计说明。为开发团队提供系统架构的详细技术指导，确保系统设计的一致性和可维护性。

### 0.2 文档范围
本文档系统地描述了Vue3在线商城系统的设计约束、设计策略、总体结构和架构设计等技术细节，涵盖前端架构、数据流设计、组件结构、API设计等方面。

### 0.3 读者对象
设计人员、开发人员、系统架构师、技术负责人。

### 0.4 参考文档
- Vue3官方架构文档
- Pinia状态管理最佳实践
- RESTful API设计规范
- 前端工程化最佳实践

### 0.5 术语与缩写解释

| 缩写、术语 | 解释 |
|-----------|------|
| SFC | 单文件组件（Single File Component） |
| SSR | 服务端渲染（Server-Side Rendering） |
| CSR | 客户端渲染（Client-Side Rendering） |
| HMR | 热模块替换（Hot Module Replacement） |
| MVVM | 模型-视图-视图模型（Model-View-ViewModel） |

## 1. 系统概述

Vue3在线商城系统是一个基于现代前端技术栈构建的单页面应用（SPA），采用组件化开发模式和响应式数据管理。系统通过Vue3的Composition API实现业务逻辑的组织，使用Pinia进行全局状态管理，通过Vue Router实现客户端路由，并使用json-server提供Mock API服务。

系统架构遵循MVVM设计模式，通过数据驱动视图更新，实现了高度的组件复用性和可维护性。

## 2. 设计约束

- 前端框架限定为Vue3，使用Composition API开发模式
- 状态管理必须使用Pinia，不使用Vuex
- 路由管理使用Vue Router 4
- 构建工具使用Vite，不使用Webpack
- 后端API使用json-server提供Mock服务
- 浏览器兼容性要求支持现代浏览器（ES6+）
- 代码必须包含详细的中文注释

## 3. 设计策略

系统采用组件化、模块化的设计策略，将复杂的业务逻辑分解为独立的功能模块。通过状态管理实现数据的集中管理和组件间通信，使用路由管理实现页面导航和状态保持。

**核心设计原则：**
- 单一职责原则：每个组件只负责特定的功能
- 开闭原则：系统对扩展开放，对修改封闭
- 依赖倒置原则：高层模块不依赖低层模块，都依赖抽象
- 组合优于继承：通过组合实现功能复用

## 4. 系统总体结构

根据需求，Vue3在线商城系统分为以下子模块：用户管理模块、商品展示模块、购物车管理模块、订单管理模块、搜索分类模块。整个系统各子模块的结构图如下所示：

```mermaid
graph TB
    A[Vue3在线商城系统] --> B[表现层 Presentation Layer]
    A --> C[业务逻辑层 Business Logic Layer]
    A --> D[数据访问层 Data Access Layer]
    A --> E[数据存储层 Data Storage Layer]
    
    B --> B1[页面组件 Views]
    B --> B2[通用组件 Components]
    B --> B3[路由管理 Router]
    
    C --> C1[状态管理 Pinia Stores]
    C --> C2[业务逻辑 Composables]
    C --> C3[工具函数 Utils]
    
    D --> D1[HTTP客户端 Axios]
    D --> D2[API接口 API Services]
    
    E --> E1[JSON数据库 db.json]
    E --> E2[本地存储 localStorage]
```

## 5. 系统架构设计

系统采用分层架构设计，从上到下分为表现层、业务逻辑层、数据访问层和数据存储层。程序整体结构如下图所示：

```mermaid
graph LR
    subgraph "前端应用 Frontend App"
        A[Vue3 SPA应用]
        B[Pinia状态管理]
        C[Vue Router路由]
        D[Axios HTTP客户端]
    end
    
    subgraph "Mock API服务 Mock API Service"
        E[json-server]
        F[RESTful API]
    end
    
    subgraph "数据存储 Data Storage"
        G[db.json文件]
        H[localStorage]
    end
    
    A --> B
    A --> C
    A --> D
    D --> F
    E --> F
    F --> G
    B --> H
```

### 技术架构图

```mermaid
graph TD
    subgraph "开发环境 Development Environment"
        A1[Vite构建工具]
        A2[HMR热重载]
        A3[ESLint代码检查]
    end
    
    subgraph "前端技术栈 Frontend Stack"
        B1[Vue3 + Composition API]
        B2[Pinia状态管理]
        B3[Vue Router 4]
        B4[Axios HTTP库]
    end
    
    subgraph "UI层 UI Layer"
        C1[响应式布局]
        C2[组件化设计]
        C3[SVG图标系统]
    end
    
    A1 --> B1
    B1 --> C1
    B2 --> C2
    B3 --> C2
    B4 --> C2
```

### 数据流架构

```mermaid
sequenceDiagram
    participant U as 用户 User
    participant V as Vue组件 Component
    participant S as Pinia Store
    participant A as API Service
    participant D as 数据库 Database
    
    U->>V: 用户操作
    V->>S: 调用Store方法
    S->>A: 发起API请求
    A->>D: 数据操作
    D-->>A: 返回数据
    A-->>S: 更新Store状态
    S-->>V: 响应式更新
    V-->>U: 界面更新

### 数据库ER图

```mermaid
erDiagram
    UserInfo {
        int id PK
        string username UK "11位数字账号"
        string nickname "用户昵称"
        string password "8-18位字母数字密码"
        string email "邮箱地址"
    }

    Categories {
        int id PK
        string firstSortName "一级分类名称"
        int fSortid FK "父级分类ID"
    }

    SecondCategories {
        int id PK
        string secondSortName "二级分类名称"
        int fSortid FK "一级分类ID"
    }

    ThirdCategories {
        int id PK
        string thirdSortName "三级分类名称"
        int fSortid FK "一级分类ID"
        int sSortid FK "二级分类ID"
    }

    Goods {
        int id PK
        string name "商品名称"
        decimal price "商品价格"
        decimal oldPrice "原价"
        json imgSrc "商品图片数组"
        string promotions "促销信息"
        json category "分类信息"
    }

    Cart {
        int id PK
        int goodsId FK "商品ID"
        int userId FK "用户ID"
        int goodsNum "商品数量"
        boolean isChecked "是否选中"
    }

    Orders {
        string id PK "订单号"
        string time "下单时间"
        decimal totalPrice "订单总价"
        string status "订单状态"
        json list "商品列表"
        int userId FK "用户ID"
    }

    UserInfo ||--o{ Cart : "用户购物车"
    UserInfo ||--o{ Orders : "用户订单"
    Goods ||--o{ Cart : "购物车商品"
    Categories ||--o{ SecondCategories : "包含二级分类"
    SecondCategories ||--o{ ThirdCategories : "包含三级分类"
    Goods }o--|| Categories : "属于分类"
```

### 用户购物流程泳道图

```mermaid
sequenceDiagram
    participant U as 用户
    participant N as 导航组件
    participant H as 首页组件
    participant G as 商品详情
    participant C as 购物车
    participant O as 订单页面
    participant S as Pinia Store
    participant A as API服务

    U->>N: 访问首页
    N->>H: 加载首页
    H->>A: 获取商品列表
    A-->>H: 返回商品数据
    H-->>U: 展示商品列表

    U->>G: 点击商品查看详情
    G->>A: 获取商品详情
    A-->>G: 返回详情数据
    G-->>U: 展示商品详情

    U->>G: 点击加入购物车
    G->>S: 调用购物车Store
    S->>A: 添加到购物车
    A-->>S: 返回成功状态
    S-->>G: 更新购物车状态
    G-->>U: 显示添加成功

    U->>C: 查看购物车
    C->>S: 获取购物车数据
    S-->>C: 返回购物车商品
    C-->>U: 展示购物车列表

    U->>C: 修改商品数量/选择商品
    C->>S: 更新购物车状态
    S-->>C: 实时计算总价
    C-->>U: 更新界面显示

    U->>O: 点击结算
    O->>A: 创建订单
    A-->>O: 返回订单信息
    O-->>U: 显示订单确认
```

### 系统用例图

```mermaid
graph TB
    subgraph "Vue3在线商城系统用例图"
        U1[游客用户]
        U2[注册用户]

        UC1[浏览商品]
        UC2[搜索商品]
        UC3[查看商品详情]
        UC4[用户注册]
        UC5[用户登录]
        UC6[添加购物车]
        UC7[管理购物车]
        UC8[查看订单]
        UC9[分类浏览]
        UC10[图片放大查看]

        U1 --> UC1
        U1 --> UC2
        U1 --> UC3
        U1 --> UC4
        U1 --> UC9
        U1 --> UC10

        U2 --> UC1
        U2 --> UC2
        U2 --> UC3
        U2 --> UC5
        U2 --> UC6
        U2 --> UC7
        U2 --> UC8
        U2 --> UC9
        U2 --> UC10

        UC6 -.-> UC5 : "需要登录"
        UC7 -.-> UC5 : "需要登录"
        UC8 -.-> UC5 : "需要登录"
    end
```

## 6. 功能模块详细设计

### 6.1 购物车流程功能描述

| 功能项 | 购物车流程 | 标识 | | 子系统 | 购物车管理子系统 |
|--------|------------|------|---|--------|------------------|
| **功能描述** | 用户登录后即可操作购物车模块进行购物，包括添加商品、修改数量、删除商品、价格计算等完整流程 |
| **使用角色** | 登录成功的用户 |

| **主要事件流程** | **用户操作** | **系统响应事件** |
|------------------|--------------|------------------|
| | 在商品详情页点击"加入购物车"按钮 | 将商品添加到购物车，显示成功提示 |
| | 点击导航栏"购物车"链接 | 跳转到购物车页面，展示所有商品 |
| | 点击商品复选框进行单选 | 更新商品选中状态，重新计算总价 |
| | 点击"全选"复选框 | 切换所有商品的选中状态 |
| | 点击数量调整按钮(+/-) | 修改商品数量，实时更新小计和总价 |
| | 点击"删除"按钮 | 弹出确认对话框，确认后删除商品 |
| | 点击"结算"按钮 | 跳转到结算页面或显示结算提示 |

| **异常事件** | **处理方式** |
|--------------|--------------|
| 用户未登录时访问购物车 | 跳转到登录页面 |
| 商品库存不足 | 显示库存不足提示 |
| 网络请求失败 | 显示错误提示，允许重试 |

| **前置条件** | 用户成功登录系统 |
|--------------|------------------|
| **后置条件** | 购物车数据更新，价格计算正确 |

| **接口说明** | **输入** | 用户选择的商品信息和数量 |
|--------------|----------|--------------------------|
| | **输出** | 购物车状态更新，价格计算结果 |

### 6.2 商品列表功能描述

| 功能项 | 商品列表展示 | 标识 | | 子系统 | 商品展示子系统 |
|--------|--------------|------|---|--------|------------------|
| **功能描述** | 在首页展示热门推荐商品列表，支持网格布局和商品点击跳转 |
| **使用角色** | 所有用户（包括游客和登录用户） |

| **主要事件流程** | **用户操作** | **系统响应事件** |
|------------------|--------------|------------------|
| | 访问首页 | 自动加载商品列表数据 |
| | 查看商品信息 | 展示商品图片、名称、价格 |
| | 点击商品 | 跳转到商品详情页面 |
| | 鼠标悬停商品 | 显示悬停效果和阴影 |

| **异常事件** | **处理方式** |
|--------------|--------------|
| API请求失败 | 使用本地模拟数据展示 |
| 图片加载失败 | 显示默认占位图 |

| **前置条件** | 无特殊要求 |
|--------------|-------------|
| **后置条件** | 商品列表正常展示 |

| **接口说明** | **输入** | 无需用户输入 |
|--------------|----------|--------------|
| | **输出** | 商品列表数据和展示界面 |

### 6.3 查看订单功能描述

| 功能项 | 订单查看管理 | 标识 | | 子系统 | 订单管理子系统 |
|--------|--------------|------|---|--------|------------------|
| **功能描述** | 用户登录后可查看个人订单列表和订单详情，支持订单删除操作 |
| **使用角色** | 登录成功的用户 |

| **主要事件流程** | **用户操作** | **系统响应事件** |
|------------------|--------------|------------------|
| | 点击"我的订单"菜单 | 验证登录状态，加载订单列表 |
| | 查看订单信息 | 展示订单号、时间、商品、金额、状态 |
| | 点击"订单详情" | 跳转到订单详情页面 |
| | 点击"删除订单" | 弹出确认对话框，确认后删除订单 |
| | 查看订单详情 | 展示完整的订单信息和商品列表 |

| **异常事件** | **处理方式** |
|--------------|--------------|
| 用户未登录 | 跳转到登录页面 |
| 订单数据加载失败 | 使用模拟数据或显示空状态 |
| 删除订单失败 | 显示错误提示信息 |

| **前置条件** | 用户必须已登录系统 |
|--------------|------------------|
| **后置条件** | 订单数据正确展示或更新 |

| **接口说明** | **输入** | 用户登录状态验证 |
|--------------|----------|------------------|
| | **输出** | 订单列表数据和操作结果 |

### 6.4 用户注册登录功能描述

| 功能项 | 用户注册登录 | 标识 | | 子系统 | 用户管理子系统 |
|--------|--------------|------|---|--------|------------------|
| **功能描述** | 提供用户注册和登录功能，包括严格的表单验证、状态管理和持久化存储 |
| **使用角色** | 未登录用户 |

| **主要事件流程** | **用户操作** | **系统响应事件** |
|------------------|--------------|------------------|
| | 填写注册表单（邮箱、昵称、账号、密码） | 实时验证表单字段格式 |
| | 提交注册信息 | 验证账号唯一性，创建用户，跳转登录页 |
| | 填写登录表单（账号、密码） | 验证账号密码匹配 |
| | 提交登录信息 | 生成token，保存用户状态，跳转首页 |
| | 点击退出登录 | 清除localStorage，更新全局状态 |

| **异常事件** | **处理方式** |
|--------------|--------------|
| 账号格式错误 | 提示"请输入正确的账号：11位数字账号" |
| 密码格式错误 | 提示"请输入8-18位包含字母和数字的密码" |
| 邮箱格式错误 | 提示"请输入正确的邮箱格式：<EMAIL>" |
| 账号已存在 | 提示"账号已存在" |
| 登录失败 | 提示"账号或密码错误" |

| **前置条件** | 无特殊要求 |
|--------------|-------------|
| **后置条件** | 用户状态正确保存到localStorage |

| **接口说明** | **输入** | 用户注册/登录表单数据 |
|--------------|----------|---------------------|
| | **输出** | 用户信息和token，状态更新结果 |

### 6.5 商品搜索分类功能描述

| 功能项 | 商品搜索分类 | 标识 | | 子系统 | 搜索分类子系统 |
|--------|--------------|------|---|--------|------------------|
| **功能描述** | 提供商品关键词搜索和三级分类浏览功能，支持搜索结果展示和分类导航 |
| **使用角色** | 所有用户 |

| **主要事件流程** | **用户操作** | **系统响应事件** |
|------------------|--------------|------------------|
| | 在搜索框输入关键词 | 实时验证输入内容 |
| | 点击搜索按钮或按回车 | 跳转搜索结果页面，展示匹配商品 |
| | 点击"全部分类" | 跳转分类页面，展示三级分类结构 |
| | 点击一级分类 | 展开显示二级分类 |
| | 点击二级/三级分类 | 跳转分类详情页，展示该分类商品 |
| | 点击搜索结果商品 | 跳转商品详情页 |

| **异常事件** | **处理方式** |
|--------------|--------------|
| 搜索关键词为空 | 不执行搜索操作 |
| 无搜索结果 | 显示"没有找到相关商品"提示 |
| 分类数据加载失败 | 使用本地模拟分类数据 |

| **前置条件** | 无特殊要求 |
|--------------|-------------|
| **后置条件** | 搜索结果或分类数据正确展示 |

| **接口说明** | **输入** | 搜索关键词或分类选择 |
|--------------|----------|------------------|
| | **输出** | 搜索结果列表或分类商品列表 |

## 7. 开发环境的配置

### 7.1 基础环境要求
- **Node.js**: 14.x 或更高版本（推荐使用 LTS 版本）
- **包管理器**: npm 或 yarn
- **操作系统**: Windows、macOS、Linux
- **编辑器**: VS Code（推荐）+ Vue 3 插件

### 7.2 项目依赖配置
基于项目实际的 package.json 配置：

```json
{
  "name": "vue-mall-simple",
  "version": "1.0.0",
  "description": "Vue3在线商城系统",
  "scripts": {
    "dev": "vite",
    "build": "vite build",
    "preview": "vite preview"
  },
  "dependencies": {
    "axios": "^1.3.4",
    "json-server": "^0.17.4",
    "pinia": "^2.0.32",
    "vue": "^3.2.47",
    "vue-router": "^4.1.6"
  },
  "devDependencies": {
    "@vitejs/plugin-vue": "^4.1.0",
    "vite": "^4.2.0"
  }
}
```

### 7.3 开发工具配置
- **构建工具**: Vite 4.2.0（快速构建和热重载）
- **状态管理**: Pinia 2.0.32（替代 Vuex）
- **路由管理**: Vue Router 4.1.6
- **HTTP客户端**: Axios 1.3.4
- **Mock服务**: json-server 0.17.4

### 7.4 项目启动步骤
1. **安装依赖**
   ```bash
   npm install
   ```

2. **启动后端API服务**
   ```bash
   npx json-server --watch db.json --port 3000
   ```

3. **启动前端开发服务器**
   ```bash
   npm run dev
   ```

4. **访问应用**
   - 前端应用：http://localhost:5173
   - API服务：http://localhost:3000

### 7.5 开发规范
- 组件命名使用 PascalCase
- 文件命名使用 kebab-case
- 所有代码必须包含详细的中文注释
- 遵循 Vue3 Composition API 开发规范
- 使用 ES6+ 语法标准

## 8. 测试环境的配置

### 8.1 功能测试
基于项目实际实现的测试流程：

**用户注册登录测试：**
- 注册表单验证测试（邮箱、账号、密码格式）
- 登录功能测试（账号密码验证）
- 用户状态持久化测试（localStorage）

**商品浏览测试：**
- 首页商品列表展示测试
- 商品详情页面测试
- 图片放大功能测试（鼠标悬停效果）

**购物车功能测试：**
- 添加商品到购物车测试
- 全选/单选功能测试
- 数量修改和价格计算测试
- 删除商品功能测试

**搜索分类测试：**
- 关键词搜索功能测试
- 三级分类浏览测试
- 搜索结果展示测试

### 8.2 API测试
使用 json-server 提供的 Mock API 进行测试：
- GET /userInfo - 用户信息获取
- POST /userInfo - 用户注册
- GET /goods - 商品列表获取
- GET /categories - 分类数据获取
- GET /cart - 购物车数据获取
- GET /orders - 订单数据获取

### 8.3 兼容性测试
- 现代浏览器兼容性测试（Chrome、Firefox、Safari、Edge）
- 响应式布局测试（桌面端、平板端、移动端）
- SVG图标显示测试

## 9. 运行环境的配置

### 9.1 开发环境
- **前端服务**: Vite 开发服务器（端口 5173）
- **后端服务**: json-server（端口 3000）
- **热重载**: 支持代码修改实时更新
- **调试工具**: Vue DevTools 浏览器插件

### 9.2 生产环境
- **构建命令**: `npm run build`
- **输出目录**: `dist/`
- **静态文件服务器**: Nginx 或 Apache
- **资源优化**: 代码压缩、资源合并

### 9.3 部署配置
```bash
# 构建生产版本
npm run build

# 预览构建结果
npm run preview
```

**环境变量配置：**
- API_BASE_URL: API服务基础地址
- NODE_ENV: 环境标识（development/production）

## 10. 其它

### 10.1 项目特色功能
基于实际项目实现的特色功能：

**图片放大功能：**
- 鼠标悬停触发放大镜效果
- 实时计算遮罩层位置
- 背景图片位置动态调整

**购物车状态管理：**
- Pinia 全局状态管理
- 实时价格计算
- 本地存储持久化

**用户状态持久化：**
- localStorage 存储用户信息
- 页面刷新状态保持
- 安全的 token 管理

### 10.2 性能优化策略
- **路由懒加载**: 使用 Vue Router 的动态导入
- **SVG图标**: 使用矢量图标确保清晰度
- **组件复用**: 通用组件设计提高复用性
- **数据缓存**: 合理使用本地存储减少请求

### 10.3 代码质量保证
- **详细注释**: 所有代码包含中文注释
- **规范命名**: 遵循 Vue3 命名规范
- **模块化设计**: 清晰的文件组织结构
- **错误处理**: 完善的异常处理机制
```
