// 订单状态管理 - 使用Pinia
import { ref, computed } from 'vue'
import { defineStore } from 'pinia'
import http from '@/utils/http.js'

export const useOrderStore = defineStore('order', () => {
  // 订单列表
  const orders = ref([])
  
  // 当前查看的订单
  const currentOrder = ref(null)

  // 计算属性：订单总数
  const orderCount = computed(() => {
    return orders.value.length
  })

  // 计算属性：按状态分组的订单
  const ordersByStatus = computed(() => {
    const grouped = {}
    orders.value.forEach(order => {
      if (!grouped[order.status]) {
        grouped[order.status] = []
      }
      grouped[order.status].push(order)
    })
    return grouped
  })

  // 创建订单
  const createOrder = async (orderData) => {
    try {
      const response = await http.post('/orders', orderData)
      
      if (response.status === 201) {
        // 添加到本地订单列表
        orders.value.unshift(response.data)
        return response.data
      }
      return null
    } catch (error) {
      console.error('创建订单失败:', error)
      return null
    }
  }

  // 获取订单列表
  const fetchOrders = async (userId = null) => {
    try {
      let url = '/orders'
      if (userId) {
        url += `?userId=${userId}`
      }
      
      const response = await http.get(url)
      orders.value = response.data || []
      return orders.value
    } catch (error) {
      console.error('获取订单列表失败:', error)
      orders.value = []
      return []
    }
  }

  // 根据ID获取订单详情
  const getOrderById = async (orderId) => {
    try {
      // 先从本地查找
      const localOrder = orders.value.find(order => order.id === orderId)
      if (localOrder) {
        currentOrder.value = localOrder
        return localOrder
      }

      // 如果本地没有，从服务器获取
      const response = await http.get(`/orders/${orderId}`)
      if (response.data) {
        currentOrder.value = response.data
        return response.data
      }
      return null
    } catch (error) {
      console.error('获取订单详情失败:', error)
      return null
    }
  }

  // 更新订单状态
  const updateOrderStatus = async (orderId, newStatus) => {
    try {
      const response = await http.patch(`/orders/${orderId}`, {
        status: newStatus
      })
      
      if (response.status === 200) {
        // 更新本地订单状态
        const order = orders.value.find(order => order.id === orderId)
        if (order) {
          order.status = newStatus
        }
        
        // 如果是当前查看的订单，也要更新
        if (currentOrder.value && currentOrder.value.id === orderId) {
          currentOrder.value.status = newStatus
        }
        
        return true
      }
      return false
    } catch (error) {
      console.error('更新订单状态失败:', error)
      return false
    }
  }

  // 删除订单
  const deleteOrder = async (orderId) => {
    try {
      const response = await http.delete(`/orders/${orderId}`)
      
      if (response.status === 200) {
        // 从本地订单列表中移除
        const index = orders.value.findIndex(order => order.id === orderId)
        if (index > -1) {
          orders.value.splice(index, 1)
        }
        
        // 如果删除的是当前查看的订单，清空当前订单
        if (currentOrder.value && currentOrder.value.id === orderId) {
          currentOrder.value = null
        }
        
        return true
      }
      return false
    } catch (error) {
      console.error('删除订单失败:', error)
      return false
    }
  }

  // 清空订单列表
  const clearOrders = () => {
    orders.value = []
    currentOrder.value = null
  }

  // 生成订单号
  const generateOrderId = () => {
    const timestamp = Date.now()
    const random = Math.floor(Math.random() * 1000).toString().padStart(3, '0')
    return `ORD${timestamp}${random}`
  }

  return {
    orders,
    currentOrder,
    orderCount,
    ordersByStatus,
    createOrder,
    fetchOrders,
    getOrderById,
    updateOrderStatus,
    deleteOrder,
    clearOrders,
    generateOrderId
  }
})
