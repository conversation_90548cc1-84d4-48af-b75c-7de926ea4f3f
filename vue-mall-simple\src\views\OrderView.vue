<template>
  <!-- 订单列表页面 -->
  <div class="order-panel">
    <NavView></NavView>
    <div
      class="order-list main-body"
      :style="{ width: globalInfo.bodyWidth + 'px' }"
    >
      <div class="order-header">
        <h2>我的订单</h2>
      </div>
      
      <div class="order-content" v-if="orderList.length > 0">
        <div class="order-title">
          <div class="pagename"><span>订单信息</span></div>
          <div class="order-detail"><span>订单详情</span></div>
          <div class="receive-people"><span>收货人</span></div>
          <div class="money"><span>金额</span></div>
          <div class="status"><span>状态</span></div>
          <div class="handel"><span>操作</span></div>
        </div>
        
        <div class="items">
          <div class="item" v-for="order in orderStore.orders" :key="order.id">
            <div class="item-title">
              <div class="time">{{ order.time }}</div>
              <div class="order-code">
                <span>订单号：{{ order.id }}</span>
              </div>
            </div>
            <div class="content">
              <div class="content-item">
                <div v-for="item in order.list" :key="item.id" class="item-goods">
                  <div class="img"><img :src="item.imgSrc[0]" /></div>
                  <div class="goods-name">{{ item.name }}</div>
                  <div class="goods-num">x{{ item.goodsNum }}</div>
                </div>
              </div>

              <div class="content-right">
                <div class="nickname">
                  {{ userStore.userInfo?.nickname }} 
                  <i class="iconfont icon-shouye1">🏠</i>
                </div>

                <div class="content-money">
                  <i class="iconfont icon-qian">¥</i>{{ order.totalPrice }}
                </div>
                <div class="content-status">
                  <span>{{ order.status || '已完成' }}</span>
                  <div class="line"></div>
                  <a href="javascript:;" @click="goToOrderDetail(order.id)"
                    >订单详情</a
                  >
                </div>
                <div class="del-order" @click="removeOrder(order.id)">
                  <span>删除</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
      
      <!-- 空订单状态 -->
      <div class="empty-order" v-else-if="orderStore.orders.length === 0">
        <div class="empty-content">
          <div class="empty-icon">📋</div>
          <h3>暂无订单</h3>
          <p>您还没有任何订单记录</p>
          <a href="javascript:;" @click="goToHome" class="go-shopping">去购物</a>
        </div>
      </div>
    </div>
    <FooterView></FooterView>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import NavView from '@/components/NavView.vue'
import FooterView from '@/components/FooterView.vue'
import { useUserStore } from '@/stores/user.js'
import { useOrderStore } from '@/stores/order.js'
import { globalInfo } from '@/utils/global.js'
import http from '@/utils/http.js'

export default {
  name: 'OrderView',
  components: {
    NavView,
    FooterView
  },
  setup() {
    const router = useRouter()
    const userStore = useUserStore()

    // 响应式数据
    const orderList = ref([])

    // 组件挂载时加载订单数据
    onMounted(async () => {
      if (!userStore.isLoggedIn()) {
        alert('请先登录')
        router.push('/login')
        return
      }
      await loadOrders()
    })

    // 加载订单数据
    const loadOrders = async () => {
      try {
        const userId = userStore.userInfo?.id
        await orderStore.fetchOrders(userId)
      } catch (error) {
        console.error('加载订单失败:', error)
      }
    }

    // 跳转到订单详情
    const goToOrderDetail = (orderId) => {
      router.push(`/order/${orderId}`)
    }

    // 删除订单
    const removeOrder = async (orderId) => {
      if (confirm('确定要删除这个订单吗？')) {
        const success = await orderStore.deleteOrder(orderId)
        if (success) {
          alert('订单删除成功')
        } else {
          alert('删除失败，请重试')
        }
      }
    }

    // 返回首页
    const goToHome = () => {
      router.push('/')
    }

    return {
      globalInfo,
      userStore,
      orderStore,
      goToOrderDetail,
      removeOrder,
      goToHome
    }
  }
}
</script>

<style scoped>
/* 订单页面样式 */
.order-panel {
  min-height: 100vh;
  background: #f5f5f5;
}

.order-list {
  margin: 20px auto;
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.order-header {
  padding: 30px;
  border-bottom: 1px solid #e0e0e0;
  text-align: center;
}

.order-header h2 {
  color: #333;
  font-size: 28px;
  margin: 0;
}

.order-content {
  padding: 20px;
}

.order-title {
  display: flex;
  background: #f8f8f8;
  padding: 15px 20px;
  border-radius: 4px;
  margin-bottom: 20px;
  font-weight: bold;
  color: #333;
}

.order-title > div {
  flex: 1;
  text-align: center;
}

.pagename {
  flex: 2;
  text-align: left;
}

.item {
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 20px;
  overflow: hidden;
}

.item-title {
  background: #f8f8f8;
  padding: 15px 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  border-bottom: 1px solid #e0e0e0;
}

.time {
  color: #666;
  font-size: 14px;
}

.order-code {
  color: #333;
  font-weight: bold;
}

.content {
  padding: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.content-item {
  flex: 2;
}

.item-goods {
  display: flex;
  align-items: center;
  gap: 15px;
  margin-bottom: 15px;
}

.item-goods:last-child {
  margin-bottom: 0;
}

.img img {
  width: 60px;
  height: 60px;
  object-fit: cover;
  border-radius: 4px;
}

.goods-name {
  flex: 1;
  color: #333;
  line-height: 1.4;
}

.goods-num {
  color: #666;
  font-size: 14px;
}

.content-right {
  display: flex;
  align-items: center;
  gap: 30px;
}

.nickname {
  display: flex;
  align-items: center;
  gap: 5px;
  color: #333;
}

.content-money {
  color: #ff6600;
  font-size: 18px;
  font-weight: bold;
}

.content-status {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 10px;
}

.content-status span {
  color: #28a745;
  font-weight: bold;
}

.line {
  width: 1px;
  height: 20px;
  background: #e0e0e0;
}

.content-status a {
  color: #ff6600;
  text-decoration: none;
  font-size: 14px;
}

.content-status a:hover {
  text-decoration: underline;
}

.del-order {
  cursor: pointer;
  color: #666;
  font-size: 14px;
}

.del-order:hover {
  color: #ff6600;
}

/* 空订单样式 */
.empty-order {
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.empty-content h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.empty-content p {
  color: #666;
  margin-bottom: 30px;
}

.go-shopping {
  background: #ff6600;
  color: white;
  padding: 12px 30px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
}

.go-shopping:hover {
  background: #e55a00;
}

.main-body {
  max-width: 1200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-title {
    display: none;
  }
  
  .content {
    flex-direction: column;
    align-items: flex-start;
    gap: 20px;
  }
  
  .content-right {
    width: 100%;
    justify-content: space-between;
  }
  
  .item-goods {
    flex-direction: column;
    align-items: flex-start;
    text-align: left;
  }
}
</style>
