<template>
  <!-- 订单详情页面 -->
  <div class="order-detail-panel">
    <NavView></NavView>
    <div
      class="order-detail-content main-body"
      :style="{ width: globalInfo.bodyWidth + 'px' }"
    >
      <div class="breadcrumb">
        <span @click="goToHome">首页</span>
        <span> > </span>
        <span @click="goToOrder">我的订单</span>
        <span> > </span>
        <span>订单详情</span>
      </div>
      
      <div class="order-info" v-if="orderDetail.id">
        <div class="order-header">
          <h2>订单详情</h2>
          <div class="order-status">
            <span class="status">{{ orderDetail.status || '已完成' }}</span>
          </div>
        </div>
        
        <div class="order-basic-info">
          <div class="info-item">
            <label>订单号：</label>
            <span>{{ orderDetail.id }}</span>
          </div>
          <div class="info-item">
            <label>下单时间：</label>
            <span>{{ orderDetail.time }}</span>
          </div>
          <div class="info-item">
            <label>订单金额：</label>
            <span class="price">¥{{ orderDetail.totalPrice }}</span>
          </div>
        </div>
        
        <div class="order-goods">
          <h3>商品清单</h3>
          <div class="goods-list">
            <div 
              class="goods-item" 
              v-for="item in orderDetail.list" 
              :key="item.id"
            >
              <img :src="item.imgSrc[0]" :alt="item.name" />
              <div class="goods-info">
                <h4>{{ item.name }}</h4>
                <div class="goods-details">
                  <span class="quantity">数量：{{ item.goodsNum }}</span>
                  <span class="unit-price">单价：¥{{ item.price }}</span>
                  <span class="subtotal">小计：¥{{ (item.price * item.goodsNum).toFixed(2) }}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
        
        <div class="order-actions">
          <button @click="goToOrder" class="btn-secondary">返回订单列表</button>
          <button @click="contactService" class="btn-primary">联系客服</button>
        </div>
      </div>
      
      <div class="order-not-found" v-else>
        <div class="not-found-content">
          <div class="not-found-icon">📋</div>
          <h3>订单不存在</h3>
          <p>该订单可能已被删除或不存在</p>
          <a href="javascript:;" @click="goToOrder" class="go-back">返回订单列表</a>
        </div>
      </div>
    </div>
    <FooterView></FooterView>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRoute, useRouter } from 'vue-router'
import NavView from '@/components/NavView.vue'
import FooterView from '@/components/FooterView.vue'
import { useUserStore } from '@/stores/user.js'
import { useOrderStore } from '@/stores/order.js'
import { globalInfo } from '@/utils/global.js'
import http from '@/utils/http.js'

export default {
  name: 'OrderDetailView',
  components: {
    NavView,
    FooterView
  },
  setup() {
    const route = useRoute()
    const router = useRouter()

    // 响应式数据
    const orderDetail = ref({})

    // 组件挂载时加载订单详情
    onMounted(async () => {
      await loadOrderDetail()
    })

    // 加载订单详情
    const loadOrderDetail = async () => {
      try {
        const orderId = route.params.id
        const response = await http.get(`/orders/${orderId}`)
        orderDetail.value = response.data
      } catch (error) {
        console.error('加载订单详情失败:', error)
        // 如果请求失败，使用模拟数据
        orderDetail.value = {
          id: '202401010001',
          time: '2024-01-01 10:30:00',
          totalPrice: 6999,
          status: '已完成',
          list: [
            {
              id: 1,
              name: '华为 Mate 60 Pro 智能手机',
              price: 6999,
              imgSrc: ['/images/phone-huawei.svg'],
              goodsNum: 1
            }
          ]
        }
      }
    }

    // 跳转到首页
    const goToHome = () => {
      router.push('/')
    }

    // 跳转到订单列表
    const goToOrder = () => {
      router.push('/order')
    }

    // 联系客服
    const contactService = () => {
      alert('客服功能开发中...')
    }

    return {
      globalInfo,
      orderDetail,
      goToHome,
      goToOrder,
      contactService
    }
  }
}
</script>

<style scoped>
/* 订单详情页面样式 */
.order-detail-panel {
  min-height: 100vh;
  background: #f5f5f5;
}

.order-detail-content {
  margin: 20px auto;
  background: white;
  border-radius: 8px;
  padding: 30px;
}

.breadcrumb {
  margin-bottom: 30px;
  color: #666;
}

.breadcrumb span {
  cursor: pointer;
}

.breadcrumb span:hover {
  color: #ff6600;
}

.order-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 30px;
  padding-bottom: 20px;
  border-bottom: 1px solid #e0e0e0;
}

.order-header h2 {
  color: #333;
  margin: 0;
}

.status {
  background: #28a745;
  color: white;
  padding: 6px 12px;
  border-radius: 4px;
  font-size: 14px;
}

.order-basic-info {
  margin-bottom: 30px;
  padding: 20px;
  background: #f8f9fa;
  border-radius: 8px;
}

.info-item {
  display: flex;
  margin-bottom: 10px;
}

.info-item:last-child {
  margin-bottom: 0;
}

.info-item label {
  width: 100px;
  color: #666;
  font-weight: bold;
}

.info-item span {
  color: #333;
}

.price {
  color: #ff6600;
  font-weight: bold;
  font-size: 18px;
}

.order-goods {
  margin-bottom: 30px;
}

.order-goods h3 {
  color: #333;
  margin-bottom: 20px;
  padding-bottom: 10px;
  border-bottom: 1px solid #e0e0e0;
}

.goods-item {
  display: flex;
  padding: 20px;
  border: 1px solid #e0e0e0;
  border-radius: 8px;
  margin-bottom: 15px;
}

.goods-item:last-child {
  margin-bottom: 0;
}

.goods-item img {
  width: 100px;
  height: 100px;
  object-fit: cover;
  border-radius: 4px;
  margin-right: 20px;
}

.goods-info {
  flex: 1;
}

.goods-info h4 {
  color: #333;
  margin-bottom: 15px;
  line-height: 1.4;
}

.goods-details {
  display: flex;
  gap: 20px;
  color: #666;
  font-size: 14px;
}

.subtotal {
  color: #ff6600;
  font-weight: bold;
}

.order-actions {
  display: flex;
  justify-content: center;
  gap: 20px;
  padding-top: 30px;
  border-top: 1px solid #e0e0e0;
}

.btn-secondary,
.btn-primary {
  padding: 12px 30px;
  border-radius: 4px;
  font-weight: bold;
  cursor: pointer;
  border: none;
  transition: all 0.3s;
}

.btn-secondary {
  background: #f8f9fa;
  color: #666;
  border: 1px solid #e0e0e0;
}

.btn-secondary:hover {
  background: #e9ecef;
}

.btn-primary {
  background: #ff6600;
  color: white;
}

.btn-primary:hover {
  background: #e55a00;
}

/* 订单不存在样式 */
.order-not-found {
  padding: 80px 20px;
  text-align: center;
}

.not-found-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.not-found-content h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.not-found-content p {
  color: #666;
  margin-bottom: 30px;
}

.go-back {
  background: #ff6600;
  color: white;
  padding: 12px 30px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
}

.go-back:hover {
  background: #e55a00;
}

.main-body {
  max-width: 1200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .order-header {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }
  
  .goods-item {
    flex-direction: column;
  }
  
  .goods-item img {
    width: 100%;
    height: 200px;
    margin-right: 0;
    margin-bottom: 15px;
  }
  
  .goods-details {
    flex-direction: column;
    gap: 5px;
  }
  
  .order-actions {
    flex-direction: column;
  }
}
</style>
