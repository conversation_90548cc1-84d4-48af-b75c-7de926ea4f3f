<template>
  <!-- 首页 -->
  <div class="home-panel">
    <!-- 导航栏 -->
    <NavView></NavView>
    
    <!-- 轮播图 -->
    <LunboTu :imgSrc="imgSrc" :width="globalInfo.bodyWidth"></LunboTu>
    
    <!-- 热门推荐 -->
    <div class="hot-panel">
      <div class="hot-title">
        <div class="title-text">
          <h3>热门推荐</h3>
        </div>
      </div>

      <div
        class="hot-content main-body"
        :style="{ width: globalInfo.bodyWidth + 'px' }"
      >
        <div class="good-items">
          <div
            class="good-item"
            v-for="item in goodsList"
            :key="item.id"
            @click="goToGoodsDetail(item)"
          >
            <img :src="item.imgSrc[0]" :alt="item.name" />
            <div class="intro">
              <span>{{ item.name }}</span>
            </div>
            <div class="price">
              <i class="iconfont icon-qian">¥</i>
              <span>{{ item.price }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 底部信息 -->
    <FooterView></FooterView>
  </div>
</template>

<script>
import { ref, onMounted } from 'vue'
import { useRouter } from 'vue-router'
import NavView from '@/components/NavView.vue'
import FooterView from '@/components/FooterView.vue'
import LunboTu from '@/components/LunboTu.vue'
import { globalInfo } from '@/utils/global.js'
import http from '@/utils/http.js'

export default {
  name: 'HomeView',
  components: {
    NavView,
    FooterView,
    LunboTu
  },
  setup() {
    const router = useRouter()

    // 响应式数据
    const imgSrc = ref([
      '/images/banner-1.svg',
      '/images/banner-2.svg',
      '/images/banner-3.svg'
    ])
    const goodsList = ref([])

    // 组件挂载时加载数据
    onMounted(async () => {
      await loadGoods()
    })

    // 加载商品数据
    const loadGoods = async () => {
      try {
        const response = await http.get('/goods')
        goodsList.value = response.data
      } catch (error) {
        console.error('加载商品失败:', error)
        // 如果请求失败，使用模拟数据
        goodsList.value = [
          {
            id: 1,
            name: '华为 Mate 60 Pro 智能手机',
            price: 6999,
            imgSrc: ['/images/phone-huawei.svg']
          },
          {
            id: 2,
            name: '苹果 iPhone 15 Pro Max',
            price: 9999,
            imgSrc: ['/images/phone-iphone.svg']
          },
          {
            id: 3,
            name: '小米13 Ultra 旗舰手机',
            price: 5999,
            imgSrc: ['/images/phone-xiaomi.svg']
          },
          {
            id: 4,
            name: '联想ThinkPad X1 Carbon',
            price: 12999,
            imgSrc: ['/images/laptop-thinkpad.svg']
          }
        ]
      }
    }

    // 跳转到商品详情页
    const goToGoodsDetail = (item) => {
      router.push(`/goods/${item.id}`)
    }

    return {
      globalInfo,
      imgSrc,
      goodsList,
      goToGoodsDetail
    }
  }
}
</script>

<style scoped>
/* 首页样式 */
.home-panel {
  min-height: 100vh;
  background: #f5f5f5;
}

.hot-panel {
  background: white;
  margin: 20px auto;
  padding: 30px 0;
}

.hot-title {
  text-align: center;
  margin-bottom: 30px;
}

.title-text h3 {
  font-size: 28px;
  color: #333;
  margin: 0;
  font-weight: bold;
  position: relative;
  display: inline-block;
}

.title-text h3::after {
  content: '';
  position: absolute;
  bottom: -10px;
  left: 50%;
  transform: translateX(-50%);
  width: 60px;
  height: 3px;
  background: #ff6600;
}

.hot-content {
  margin: 0 auto;
}

.good-items {
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
  gap: 20px;
  padding: 0 20px;
}

.good-item {
  background: white;
  border-radius: 8px;
  overflow: hidden;
  box-shadow: 0 2px 8px rgba(0, 0, 0, 0.1);
  cursor: pointer;
  transition: all 0.3s ease;
}

.good-item:hover {
  transform: translateY(-5px);
  box-shadow: 0 8px 25px rgba(0, 0, 0, 0.15);
}

.good-item img {
  width: 100%;
  height: 200px;
  object-fit: contain;
  display: block;
  background: #f8f8f8;
  padding: 10px;
  border-radius: 8px 8px 0 0;
}

.intro {
  padding: 15px;
}

.intro span {
  font-size: 16px;
  color: #333;
  line-height: 1.4;
  display: -webkit-box;
  -webkit-line-clamp: 2;
  -webkit-box-orient: vertical;
  overflow: hidden;
  text-overflow: ellipsis;
}

.price {
  padding: 0 15px 15px;
  display: flex;
  align-items: center;
  gap: 5px;
}

.price i {
  color: #ff6600;
  font-size: 16px;
  font-weight: bold;
}

.price span {
  color: #ff6600;
  font-size: 20px;
  font-weight: bold;
}

.main-body {
  max-width: 1200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .good-items {
    grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
    gap: 15px;
    padding: 0 10px;
  }
  
  .good-item img {
    height: 150px;
    padding: 5px;
  }
  
  .intro {
    padding: 10px;
  }
  
  .intro span {
    font-size: 14px;
  }
  
  .price {
    padding: 0 10px 10px;
  }
  
  .price span {
    font-size: 18px;
  }
}
</style>
