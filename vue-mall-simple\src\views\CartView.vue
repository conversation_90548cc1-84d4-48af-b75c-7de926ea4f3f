<template>
  <!-- 购物车页面 -->
  <div class="cart-panel">
    <NavView></NavView>
    <div class="header">
      <div
        class="main-header"
        :style="{ width: globalInfo.bodyWidth + 'px' }"
      >
        <div class="text">
          <h2>我的购物车</h2>
          <p>温馨提示：产品是否购买成功，以最终下单为准哦，请尽快结算</p>
        </div>
      </div>
    </div>
    <div class="content">
      <div class="main" :style="{ width: globalInfo.bodyWidth + 'px' }">
        <div class="container">
          <div class="cart-goods-list" v-if="cartStore.cartItems.length > 0">
            <div class="list-head clearfix">
              <ul>
                <li>
                  <input
                    type="checkbox"
                    style="margin-right: 10px"
                    :checked="cartStore.isAllSelected"
                    @change="toggleAllCheck"
                  /><span>全选</span>
                </li>
                <li><span>商品名称</span></li>
                <li><span>单价</span></li>
                <li><span>数量</span></li>
                <li><span>小计</span></li>
                <li><span>操作</span></li>
              </ul>
            </div>
            <div class="list-body">
              <div class="items clearfix" v-for="item in cartStore.cartItems" :key="item.id">
                <div class="check">
                  <input
                    type="checkbox"
                    :checked="item.isChecked"
                    @change="toggleItemCheck(item.id)"
                  />
                </div>
                <div class="shopImg">
                  <img :src="item.imgSrc[0]" style="width: 85px" />
                </div>
                <div class="shopName">{{ item.name }}</div>
                <div class="shopPrice">¥{{ item.price }}</div>
                <div class="shopCount">
                  <div class="shopCount-style clearfix">
                    <a href="javascript:;" @click="decreaseQuantity(item.id)">-</a>
                    <span style="color: #424242; font-size: 16px">
                      {{ item.goodsNum }}</span
                    >
                    <a href="javascript:;" @click="increaseQuantity(item.id)">+</a>
                  </div>
                </div>
                <div class="shopTotal">¥{{ (item.goodsNum * item.price).toFixed(2) }}</div>
                <div class="del">
                  <div class="del-i">
                    <a href="javascript:;" @click="removeItem(item.id)">删除</a>
                  </div>
                </div>
              </div>

              <div class="list-bottom clearfix">
                <div class="left">
                  <a href="javascript:;" @click="goToHome">继续购物</a>
                  <p>
                    已选择<span>{{ cartStore.selectedItemsCount }}</span>件
                  </p>
                </div>
                <div class="right">
                  <p>
                    合计：<span>¥{{ cartStore.selectedItemsPrice.toFixed(2) }}</span>
                  </p>
                  <a
                    href="javascript:;"
                    @click="goToOrder"
                    class="checkout-btn"
                    >去结算</a
                  >
                </div>
              </div>
            </div>
          </div>
          
          <!-- 空购物车状态 -->
          <div class="empty-cart" v-else>
            <div class="empty-content">
              <div class="empty-icon">🛒</div>
              <h3>购物车是空的</h3>
              <p>快去挑选喜欢的商品吧</p>
              <a href="javascript:;" @click="goToHome" class="go-shopping">去购物</a>
            </div>
          </div>
        </div>
      </div>
    </div>
    <FooterView></FooterView>
  </div>
</template>

<script>
import { useRouter } from 'vue-router'
import NavView from '@/components/NavView.vue'
import FooterView from '@/components/FooterView.vue'
import { useCartStore } from '@/stores/cart.js'
import { useUserStore } from '@/stores/user.js'
import { globalInfo } from '@/utils/global.js'
import http from '@/utils/http.js'

export default {
  name: 'CartView',
  components: {
    NavView,
    FooterView
  },
  setup() {
    const router = useRouter()
    const cartStore = useCartStore()
    const userStore = useUserStore()

    // 切换全选状态
    const toggleAllCheck = (event) => {
      cartStore.toggleAllCheck(event.target.checked)
    }

    // 切换单个商品选中状态
    const toggleItemCheck = (goodsId) => {
      cartStore.toggleItemCheck(goodsId)
    }

    // 增加商品数量
    const increaseQuantity = async (goodsId) => {
      await cartStore.increaseQuantity(goodsId)
    }

    // 减少商品数量
    const decreaseQuantity = async (goodsId) => {
      await cartStore.decreaseQuantity(goodsId)
    }

    // 移除商品
    const removeItem = async (goodsId) => {
      if (confirm('确定要删除这个商品吗？')) {
        const success = await cartStore.removeFromCart(goodsId)
        if (success) {
          alert('商品已删除')
        } else {
          alert('删除失败，请重试')
        }
      }
    }

    // 返回首页
    const goToHome = () => {
      router.push('/')
    }

    // 去结算
    const goToOrder = async () => {
      const selectedItems = cartStore.getSelectedItems()

      if (selectedItems.length === 0) {
        alert('请选择要结算的商品')
        return
      }

      if (!userStore.isLoggedIn()) {
        alert('请先登录')
        router.push('/login')
        return
      }

      try {
        // 生成订单数据
        const orderId = 'ORD' + Date.now()
        const orderData = {
          id: orderId,
          time: new Date().toLocaleString('zh-CN'),
          userId: userStore.userInfo?.id || 1,
          totalPrice: cartStore.selectedItemsPrice,
          status: '待支付',
          list: selectedItems.map(item => ({
            id: item.id,
            name: item.name,
            price: item.price,
            goodsNum: item.goodsNum,
            imgSrc: item.imgSrc
          }))
        }

        // 保存订单到服务器
        const response = await http.post('/orders', orderData)

        if (response.status === 201) {
          // 从购物车中移除已结算的商品
          for (const item of selectedItems) {
            await cartStore.removeFromCart(item.id)
          }

          alert('订单创建成功！')
          router.push(`/order/${orderId}`)
        } else {
          alert('订单创建失败，请重试')
        }
      } catch (error) {
        console.error('结算失败:', error)
        alert('结算失败，请重试')
      }
    }

    return {
      globalInfo,
      cartStore,
      userStore,
      toggleAllCheck,
      toggleItemCheck,
      increaseQuantity,
      decreaseQuantity,
      removeItem,
      goToHome,
      goToOrder
    }
  }
}
</script>

<style scoped>
/* 购物车页面样式 */
.cart-panel {
  min-height: 100vh;
  background: #f5f5f5;
}

.header {
  background: white;
  padding: 20px 0;
  margin-bottom: 20px;
}

.main-header {
  margin: 0 auto;
}

.text h2 {
  color: #333;
  margin-bottom: 10px;
}

.text p {
  color: #666;
  font-size: 14px;
}

.content {
  padding-bottom: 50px;
}

.main {
  margin: 0 auto;
}

.cart-goods-list {
  background: white;
  border-radius: 8px;
  overflow: hidden;
}

.list-head {
  background: #f8f8f8;
  padding: 15px 0;
  border-bottom: 1px solid #e0e0e0;
}

.list-head ul {
  display: flex;
  align-items: center;
  padding: 0 20px;
}

.list-head li {
  flex: 1;
  text-align: center;
  font-weight: bold;
  color: #333;
}

.list-head li:first-child {
  flex: 0 0 120px;
  text-align: left;
}

.list-head li:nth-child(2) {
  flex: 2;
  text-align: left;
}

.items {
  display: flex;
  align-items: center;
  padding: 20px;
  border-bottom: 1px solid #f0f0f0;
}

.check {
  flex: 0 0 40px;
}

.shopImg {
  flex: 0 0 100px;
  margin-right: 20px;
}

.shopImg img {
  width: 85px;
  height: 85px;
  object-fit: cover;
  border-radius: 4px;
}

.shopName {
  flex: 2;
  color: #333;
  line-height: 1.4;
}

.shopPrice {
  flex: 1;
  text-align: center;
  color: #ff6600;
  font-weight: bold;
}

.shopCount {
  flex: 1;
  text-align: center;
}

.shopCount-style {
  display: inline-flex;
  border: 1px solid #e0e0e0;
  border-radius: 4px;
  overflow: hidden;
}

.shopCount-style a {
  width: 30px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  background: #f5f5f5;
  color: #666;
  text-decoration: none;
}

.shopCount-style a:hover {
  background: #e0e0e0;
}

.shopCount-style span {
  width: 50px;
  height: 30px;
  display: flex;
  align-items: center;
  justify-content: center;
  border-left: 1px solid #e0e0e0;
  border-right: 1px solid #e0e0e0;
}

.shopTotal {
  flex: 1;
  text-align: center;
  color: #ff6600;
  font-weight: bold;
}

.del {
  flex: 1;
  text-align: center;
}

.del a {
  color: #666;
  text-decoration: none;
}

.del a:hover {
  color: #ff6600;
}

.list-bottom {
  padding: 20px;
  background: #f8f8f8;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.left {
  display: flex;
  align-items: center;
  gap: 20px;
}

.left a {
  color: #ff6600;
  text-decoration: none;
}

.left p span {
  color: #ff6600;
  font-weight: bold;
}

.right {
  display: flex;
  align-items: center;
  gap: 20px;
}

.right p {
  font-size: 18px;
}

.right p span {
  color: #ff6600;
  font-weight: bold;
  font-size: 20px;
}

.checkout-btn {
  background: #ff6600;
  color: white;
  padding: 12px 30px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
}

.checkout-btn:hover {
  background: #e55a00;
}

/* 空购物车样式 */
.empty-cart {
  background: white;
  border-radius: 8px;
  padding: 80px 20px;
  text-align: center;
}

.empty-icon {
  font-size: 80px;
  margin-bottom: 20px;
}

.empty-content h3 {
  color: #333;
  margin-bottom: 10px;
  font-size: 24px;
}

.empty-content p {
  color: #666;
  margin-bottom: 30px;
}

.go-shopping {
  background: #ff6600;
  color: white;
  padding: 12px 30px;
  border-radius: 4px;
  text-decoration: none;
  font-weight: bold;
}

.go-shopping:hover {
  background: #e55a00;
}

.main-body {
  max-width: 1200px;
}

/* 响应式设计 */
@media (max-width: 768px) {
  .items {
    flex-direction: column;
    align-items: flex-start;
    gap: 15px;
  }

  .list-head {
    display: none;
  }

  .list-bottom {
    flex-direction: column;
    gap: 15px;
  }
}
</style>
